房间-模板-环节系统重构方案

一、 背景与目标当前系统的“房间-模板-环节”结构，房间与模板共享着同一套环节（EventStep）对象。这种设计虽然简单，但缺乏灵活性和数据隔离性，无法满足在房间进行中动态、安全地修改活动流程的需求。本次重构的核心目标是：实现房间活动流程的实例解耦。即每个房间在创建时，都获得一份源于模板但完全独立、可自由修改的活动环节列表。这使得在一个房间内对环节的增、删、改操作，不会影响到原始模板或其他任何房间。

二、 核心挑战：共享的环节引用当前结构: Room 模型通过多对多关系直接链接到公共的 EventStep 模型。问题: 任何对 EventStep 的修改都会影响到所有引用它的房间和模板，存在数据污染风险。解决方案: 打破这种共享链接，为每个房间创建一套私有的环节实例。

三、 系统改造清单为实现上述目标，需要对模型、API和核心业务逻辑进行以下三个层面的改造。

1. 模型层改动：创建独立的“房间环节”模型这是本次重构的基石，旨在实现数据隔离。
    1.1. 创建新模型 RoomEventStep:在 core/models.py 中定义一个新模型 RoomEventStep，用于存储每个房间专属的环节列表。建议字段:class RoomEventStep(models.Model):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='event_steps') # 反向关联到房间
    order = models.PositiveIntegerField() # 环节在当前房间流程中的顺序
    name = models.CharField(max_length=100) # 从模板环节复制的名称
    step_type = models.CharField(max_length=50) # 从模板环节复制的类型
    duration = models.PositiveIntegerField() # 从模板环节复制的时长
    # ... 其他可能需要的环节配置字段

    class Meta:
        ordering = ['order'] # 默认按顺序排序
    1.2. 修改 Room 模型:移除 Room 模型中原有的与 EventStep 的多对多关系字段。将 current_event_step 外键的目标从 EventStep 修改为新的 RoomEventStep 模型。# 在 Room 模型中
# current_event_step = models.ForeignKey(EventStep, ...) # 旧
current_event_step = models.ForeignKey(RoomEventStep, on_delete=models.SET_NULL, null=True, blank=True) # 新

2. 核心业务逻辑改动：适配新数据结构模型的变更需要同步更新所有处理活动流程的核心函数。
    2.1. 修改房间创建逻辑:文件: core/utils.py 或 core/services/room_manager.py函数: create_room_from_template (或类似函数)改造点: 当根据模板创建房间时，不再是简单地关联 EventStep。而是需要遍历模板中的所有环节，并为新创建的房间生成一批全新的、独立的 RoomEventStep 记录并保存到数据库。
    2.2. 修改环节推进逻辑:文件: core/utils.py 或 core/consumers.py函数: advance_to_next_step (或类似函数)改造点: 推进活动流程时，查询逻辑需要从查找公共环节库，改为查找与当前房间关联的 RoomEventStep 列表，并根据其 order 字段来确定下一个环节。

3. API层改动：提供动态修改环节的接口为赋予房主动态管理活动流程的能力，需要提供新的API端点。
    3.1. 创建“插入环节”的API:建议路由: POST /api/rooms/{roomCode}/events/insert/核心逻辑:接收要插入的环节数据（如 name, type）和要插入的位置 order。权限验证：确保操作者是房主，且 order 值大于当前房间已完成环节的 order。事务处理:将所有 order 大于或等于插入位置的 RoomEventStep 的 order 值加1，为新环节腾出空间。创建并保存新的 RoomEventStep 对象。整个过程必须包裹在数据库事务中以保证数据一致性。
    3.2. 创建“删除环节”的API:建议路由: DELETE /api/rooms/{roomCode}/events/{stepId}/核心逻辑:权限验证：确保操作者是房主，且要删除的环节的 order 必须大于当前已完成环节的 order。事务处理:删除指定的 RoomEventStep 对象。将所有 order 大于被删除环节的 RoomEventStep 的 order 值减1，填补顺序空缺。同样需要使用数据库事务。

四、 总结通过以上三个层面的改造，我们可以将系统升级为一个更加灵活、健壮的实时互动平台。每个房间的活动流程将成为一个独立的、可塑的“剧本”，房主可以像导演一样，随时调整后续的“剧情”，极大地丰富了应用的可玩性和使用场景。


ISSUE：
1. 房间内有关时间轴添加环节的按钮应当直接使用LobbyView中添加环节的弹窗（即AddStepModel），而不是设置一个新的临时占位
2. 在登录之后，前端虽然可以正确向后端发送请求订阅等级的请求并渲染，但是不能正确根据获取的订阅等级进行相应权限的开放和限制

3. 在预约界面进行预约之后，日历没有及时更新预约信息并渲染在日历上，而且后端没有收到更新日历的请求 
4. 日历没有正常根据用户所在时区正确显示当天时间
5. 房间没有在下一环节状态刷新时更新前端显示内容
