# core/event_handlers/pictionary_handler.py

import random
import logging
from typing import Any, Dict, Optional, Tuple

from django.utils import timezone
from channels.db import database_sync_to_async
from .BaseEventHandler import BaseEventHandler
from core.models import Room, User, RoomParticipant
from events.models import EventStep
from games.models import Game, PictionaryGame
from games.words import WORD_LIST


# ============================================================================
# 你画我猜数据库辅助函数：
# ============================================================================

@database_sync_to_async
def get_pictionary_game(room_code):
    """获取一个房间的你画我猜游戏数据"""
    try:
        return PictionaryGame.objects.select_related('game__room', 'current_drawer').get(game__room__room_code=room_code, game__is_active=True)
    except PictionaryGame.DoesNotExist:
        return None
    
@database_sync_to_async
def end_pictionary_round(room_code):
    """结束一个房间的你画我猜回合"""
    try:
        room = Room.objects.get(room_code=room_code)
        if room.status == Room.STATUS_IN_PROGRESS:
            room.status = Room.STATUS_WAITING
            room.save()
            if hasattr(room, 'game'):
                room.game.is_active = False
                room.game.save()
            return room.status
        return None
    except Room.DoesNotExist:
        return None

# Updated helper functions to use RoomParticipant instead of PlayerScore
@database_sync_to_async
def update_scores(winner, drawer, room):
    """更新得分，使用新的RoomParticipant模型"""
    # 更新获胜者得分
    winner_participant = RoomParticipant.objects.get(room=room, user=winner, is_active=True)
    winner_participant.add_score(10)

    # 更新绘画者得分
    drawer_participant = RoomParticipant.objects.get(room=room, user=drawer, is_active=True)
    drawer_participant.add_score(5)

    # 获取所有参与者的得分
    participants = RoomParticipant.objects.filter(room=room, is_active=True).order_by('-score')
    return {participant.user.username: participant.score for participant in participants}


logger = logging.getLogger(__name__)

class PictionaryEventHandler(BaseEventHandler):
    """
    处理“你画我猜”游戏环节的逻辑 (已优化)
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # --- 新增 ---: 在处理器实例中缓存当前游戏状态，避免重复查询
        self.game: Optional[PictionaryGame] = None

    # ============================================================================
    # 核心数据库操作函数 (所有同步DB操作都封装在这里)
    # ============================================================================

    @database_sync_to_async
    def _setup_new_round(self, room: Room, step: EventStep) -> Dict[str, Any]:
        """
        在一个同步的数据库事务中，完成所有启动新回合所需的操作。
        这是解决 "async context" 问题的最佳实践。
        """
        # 1. 获取参与者并选择绘画者（使用新的中间模型）
        # 直接查询RoomParticipant而不是使用room.get_participants()
        from core.models import RoomParticipant
        active_participants = RoomParticipant.objects.filter(
            room=room, is_active=True
        ).select_related('user')

        participants = [p.user for p in active_participants]
        if not participants:
            raise ValueError("房间内没有参与者，无法开始游戏。")
        drawer = random.choice(participants)

        # 2. 选择一个词
        word = random.choice(WORD_LIST)

        # 3. 更新房间状态
        room.status = Room.STATUS_IN_PROGRESS
        room.save()

        # 4. 创建或更新游戏会话和具体的游戏实例
        game_session, _ = Game.objects.update_or_create(
            room=room,
            defaults={'game_type': Game.GAME_PICTIONARY, 'is_active': True}
        )
        pictionary_game, _ = PictionaryGame.objects.update_or_create(
            game=game_session,
            defaults={'current_word': word, 'current_drawer': drawer}
        )
        
        # 5. 返回所有需要的数据
        return {
            "game_instance": pictionary_game,
            "drawer_username": drawer.username,
            "word": word,
            "room_status": room.status,
        }

    @database_sync_to_async
    def _process_correct_guess(self, winner: User, game: PictionaryGame) -> Dict[str, Any]:
        """在一个同步的数据库事务中，处理猜对后的所有逻辑。"""
        room = game.game.room
        drawer = game.current_drawer

        # 1. 更新分数（使用新的RoomParticipant模型）
        try:
            winner_participant = RoomParticipant.objects.get(room=room, user=winner, is_active=True)
            winner_participant.add_score(10)
        except RoomParticipant.DoesNotExist:
            # 如果参与者记录不存在，创建一个
            winner_participant = RoomParticipant.objects.create(
                room=room, user=winner, role=RoomParticipant.ROLE_PARTICIPANT, score=10
            )

        try:
            drawer_participant = RoomParticipant.objects.get(room=room, user=drawer, is_active=True)
            drawer_participant.add_score(5)
        except RoomParticipant.DoesNotExist:
            drawer_participant = RoomParticipant.objects.create(
                room=room, user=drawer, role=RoomParticipant.ROLE_PARTICIPANT, score=5
            )

        # 获取所有参与者的得分
        participants = RoomParticipant.objects.filter(room=room, is_active=True).order_by('-score')
        scores = {participant.user.username: participant.score for participant in participants}

        # 2. 结束回合
        room.status = Room.STATUS_WAITING
        room.save()
        game.game.is_active = False
        game.game.save()

        return {
            "scores": scores,
            "room_status": room.status
        }

    # ============================================================================
    # 异步的事件处理方法 (现在变得非常简洁)
    # ============================================================================

    async def start_step(self, room: Room, step: EventStep) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """异步地启动“你画我猜”环节"""
        try:
            # 获取环节配置
            config = step.configuration or {}
            round_duration = config.get('round_duration', 60)  # 单局时长，默认60秒
            max_rounds = config.get('max_rounds', 999)  # 最大局数，默认999（基本无限制）

            # 只调用一个被装饰过的函数来处理所有数据库逻辑
            setup_data = await self._setup_new_round(room, step)

            # 将游戏实例缓存在 self.game 中
            self.game = setup_data["game_instance"]

            # 初始化多局游戏状态
            self.current_round = 1
            self.max_rounds = max_rounds
            self.round_duration = round_duration
            self.step_start_time = timezone.now()
            self.step_duration = step.duration  # 环节总时长

            # 准备要广播给前端的 payload
            return {
                "drawer": setup_data["drawer_username"],
                "word": setup_data["word"],
                "round_duration": round_duration,  # 单局时长
                "current_round": self.current_round,
                "max_rounds": self.max_rounds,
                "step_duration": self.step_duration,  # 环节总时长
                "room_status": setup_data["room_status"],
                "step_info": {"step_type": step.step_type, "order": step.order}
            }, None

        except Exception as e:
            logger.error(f"启动你画我猜游戏时出错: {e}", exc_info=True)
            return None, f"启动游戏时发生错误: {e}"

    def _has_enough_time_for_next_round(self) -> bool:
        """检查是否有足够的时间开始新一局"""
        if not hasattr(self, 'step_start_time') or not hasattr(self, 'step_duration'):
            return True  # 如果没有时间限制，允许继续

        elapsed_time = (timezone.now() - self.step_start_time).total_seconds()
        remaining_time = self.step_duration - elapsed_time

        # 需要至少有一个完整的round_duration时间才能开始新一局
        # 加上30秒的缓冲时间，确保有足够时间完成这一局
        return remaining_time >= (self.round_duration + 30)

    async def handle_message(self, user: User, payload: Dict[str, Any]) -> bool:
        """处理游戏中的猜词消息"""
        message = payload.get('message', '').strip()
        if not message or not self.game:
            return False

        # 绘画者不能猜词 (直接从缓存的self.game中获取信息)
        if user.id == self.game.current_drawer.id:
            return True  # 消息被处理，不再广播

        # 检查是否猜对
        if message.lower() == self.game.current_word.lower():
            await self._handle_correct_guess(user)
            return True # 消息被处理

        return False # 没猜对，作为普通消息广播

    async def _handle_correct_guess(self, winner: User):
        """处理正确猜词的异步流程"""
        if not self.game: return

        try:
            # 调用一个被装饰过的函数处理所有数据库逻辑
            result_data = await self._process_correct_guess(winner, self.game)

            # 检查是否还有更多局
            self.current_round += 1
            # 检查局数限制和时间限制
            has_more_rounds = (
                self.current_round <= self.max_rounds and
                self._has_enough_time_for_next_round()
            )

            # 广播回合结束消息
            await self.broadcast_to_room('broadcast_round_over', {
                'winner': winner.username,
                'word': self.game.current_word,
                'current_round': self.current_round - 1,  # 刚结束的局数
                'max_rounds': self.max_rounds,
                'has_more_rounds': has_more_rounds,
                'room_status': result_data["room_status"],
                'scores': result_data["scores"],
                'end_reason': 'correct_guess' if not has_more_rounds else None,
            })

            if has_more_rounds:
                # 开始下一局
                await self._start_next_round()
            else:
                # 所有局都结束了，清理游戏状态
                self.game = None

        except Exception as e:
            logger.error(f"处理正确猜词时出错: {e}", exc_info=True)

    async def handle_timeout(self) -> None:
        """处理单局超时"""
        if not self.game: return

        try:
            # 检查是否还有更多局
            self.current_round += 1
            # 检查局数限制和时间限制
            has_more_rounds = (
                self.current_round <= self.max_rounds and
                self._has_enough_time_for_next_round()
            )

            # 直接调用数据库函数结束回合
            room_status = await database_sync_to_async(self._end_round_sync)(self.game)

            if room_status:
                await self.broadcast_to_room('broadcast_round_over', {
                    'winner': None,
                    'word': self.game.current_word,
                    'current_round': self.current_round - 1,  # 刚结束的局数
                    'max_rounds': self.max_rounds,
                    'has_more_rounds': has_more_rounds,
                    'room_status': room_status,
                    'scores': {},
                    'timeout': True,
                    'end_reason': 'timeout' if not has_more_rounds else None,
                })

            if has_more_rounds:
                # 开始下一局
                await self._start_next_round()
            else:
                # 所有局都结束了，清理游戏状态
                self.game = None

        except Exception as e:
            logger.error(f"处理你画我猜超时出错: {e}", exc_info=True)

    async def _start_next_round(self):
        """开始下一局游戏"""
        try:
            # 选择新的绘画者和词汇
            from core.models import RoomParticipant
            active_participants = await database_sync_to_async(
                lambda: list(RoomParticipant.objects.filter(
                    room=self.game.game.room, is_active=True
                ).select_related('user'))
            )()

            participants = [p.user for p in active_participants]
            if not participants:
                logger.error("没有活跃参与者，无法开始下一局")
                return

            drawer = random.choice(participants)
            word = random.choice(WORD_LIST)

            # 更新游戏状态
            await database_sync_to_async(self._update_game_for_next_round)(drawer, word)

            # 广播新一局开始
            await self.broadcast_to_room('broadcast_new_round', {
                'drawer': drawer.username,
                'word': word,
                'round_duration': self.round_duration,
                'current_round': self.current_round,
                'max_rounds': self.max_rounds,
            })

        except Exception as e:
            logger.error(f"开始下一局时出错: {e}", exc_info=True)

    def _update_game_for_next_round(self, drawer: User, word: str):
        """同步更新游戏状态为下一局"""
        self.game.current_drawer = drawer
        self.game.current_word = word
        self.game.round_start_time = timezone.now()
        self.game.save()

    # 为 handle_timeout 创建一个同步辅助函数
    def _end_round_sync(self, game: PictionaryGame) -> str:
        room = game.game.room
        if room.status == Room.STATUS_IN_PROGRESS:
            room.status = Room.STATUS_WAITING
            room.save()
            game.game.is_active = False
            game.game.save()
            return room.status
        return room.status

    async def handle_restart(self, user, payload: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """处理你画我猜重启"""
        try:
            room = await self.get_room_with_template()
            if not room:
                return None, '房间不存在。'

            # 获取当前步骤
            current_step = await database_sync_to_async(
                lambda: room.event_template.steps.filter(order=room.current_step_order).first()
            )()

            if not current_step:
                return None, '无法找到当前环节信息。'

            # 重新启动游戏
            return await self.start_step(room, current_step)

        except Exception as e:
            self.logger.error(f"Error restarting pictionary: {e}")
            return None, "重新开始游戏时发生错误。"

    async def handle_custom_action(self, action: str, user, payload: Dict[str, Any]) -> bool:
        """处理你画我猜的自定义动作"""
        if action == 'send_drawing':
            return await self._handle_drawing_data(user, payload)
        return False

    async def _handle_drawing_data(self, user, payload: Dict[str, Any]) -> bool:
        """处理绘图数据"""
        try:
            path_data = payload.get('path_data')
            if not path_data:
                return True

            # 验证路径数据的有效性
            if not isinstance(path_data, dict) or not path_data.get('path') or not path_data.get('id'):
                self.logger.warning(f"Invalid path data received from user {user.username}")
                return True

            # 验证用户是否有绘画权限
            game = await get_pictionary_game(self.room_code)
            if game and user.id != game.current_drawer.id:
                await self.send_error_to_user("只有绘画者可以绘画。")
                return True

            # 路径长度检查（避免过长的路径导致性能问题）
            path_str = path_data.get('path', '')
            if len(path_str) > 10000:  # 限制路径长度
                self.logger.warning(f"Path too long from user {user.username}: {len(path_str)} characters")
                return True

            # 广播绘图数据
            await self.broadcast_to_room('broadcast_drawing_data', {
                'path_data': {
                    'id': path_data['id'],
                    'path': path_str,
                    'color': path_data.get('color', 'black')
                }
            })
            return True

        except Exception as e:
            self.logger.error(f"Error handling drawing data: {e}")
            return True