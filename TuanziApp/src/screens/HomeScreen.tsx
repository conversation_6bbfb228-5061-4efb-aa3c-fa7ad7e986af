import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  Alert,
  ScrollView,
  TouchableOpacity,
  Animated
} from 'react-native';
import { useAuth } from '../auth/AuthContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { API_URL } from '../api/client';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { Card, Button, Badge, Screen } from '../components';
import { theme } from '../styles/theme';
import { CheckInButton } from '../components/CheckInButton';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

// 导航卡片数据
interface NavigationCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  route: keyof RootStackParamList;
  params?: any;
  requiresSubscription?: 'Pro' | 'Max';
}

export const HomeScreen = () => {
  const { user, logout, token } = useAuth();
  const { subscriptionInfo } = useSubscription();
  const [roomCodeInput, setRoomCodeInput] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();

  // 动画值
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);

  useEffect(() => {
    // 页面加载动画
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleJoinRoom = async () => {
    if (!roomCodeInput.trim()) {
      Alert.alert('提示', '请输入房间代码');
      return;
    }

    try {
      const response = await fetch(`${API_URL}/api/rooms/join/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` },
        body: JSON.stringify({ room_code: roomCodeInput.trim() }),
      });
      const data = await response.json();
      if (response.ok) {
        navigation.navigate('Room', { room: data.room });
      } else {
        Alert.alert('加入失败', data.error || '无法加入房间。');
      }
    } catch (error) {
      console.error(error);
      Alert.alert('错误', '加入房间时发生错误。');
    }
  };

  // 导航卡片配置
  const navigationCards: NavigationCard[] = [
    {
      id: 'create-room',
      title: '创建房间',
      description: '开始一个新的游戏房间',
      icon: '🎮',
      color: theme.colors.primary,
      route: 'CreateRoom',
    },
    {
      id: 'event-designer',
      title: '环节设计器',
      description: '设计自定义游戏环节',
      icon: '🎨',
      color: theme.colors.secondary,
      route: 'EventDesigner',
      requiresSubscription: 'Pro',
    },
    {
      id: 'calendar',
      title: '日历预约',
      description: '预约未来的游戏时间',
      icon: '📅',
      color: theme.colors.tertiary,
      route: 'Calendar',
      requiresSubscription: 'Pro',
    },
    {
      id: 'subscription',
      title: '订阅管理',
      description: '管理您的订阅计划',
      icon: '💎',
      color: theme.colors.vibrant.coral,
      route: 'Subscription',
    },
  ];

  const renderNavigationCard = (card: NavigationCard) => {
    const isLocked = card.requiresSubscription &&
      (!subscriptionInfo || subscriptionInfo.current_level === 'Free');

    return (
      <Card
        key={card.id}
        style={StyleSheet.flatten([styles.navigationCard, { borderLeftColor: card.color }])}
        variant="elevated"
        onPress={() => {
          if (isLocked) {
            Alert.alert(
              '需要订阅',
              `此功能需要${card.requiresSubscription}订阅。是否前往订阅页面？`,
              [
                { text: '取消', style: 'cancel' },
                { text: '前往订阅', onPress: () => navigation.navigate('Subscription') }
              ]
            );
          } else {
            navigation.navigate(card.route as any);
          }
        }}
      >
        <View style={styles.cardContent}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardIcon}>{card.icon}</Text>
            <View style={styles.cardTitleContainer}>
              <Text style={styles.cardTitle}>{card.title}</Text>
              {isLocked && (
                <Badge
                  text={card.requiresSubscription!}
                  variant="warning"
                  size="xs"
                />
              )}
            </View>
          </View>
          <Text style={styles.cardDescription}>{card.description}</Text>
        </View>
      </Card>
    );
  };

  return (
    <Screen style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* 背景装饰 */}
        <View style={styles.backgroundDecoration}>
          <View style={styles.circle1} />
          <View style={styles.circle2} />
          <View style={styles.circle3} />
        </View>

        {/* 头部欢迎区域 - 重新设计 */}
        <Animated.View
          style={[
            styles.heroSection,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeEmoji}>🎉</Text>
            <Text style={styles.welcomeTitle}>欢迎回来</Text>
            <Text style={styles.welcomeSubtitle}>{user?.username}</Text>

            {/* 订阅状态徽章 */}
            <View style={styles.subscriptionBadge}>
              <Badge
                text={user?.subscription_level || 'Free'}
                variant={user?.subscription_level === 'Free' ? 'neutral' : 'primary'}
                size="medium"
              />
            </View>
          </View>
        </Animated.View>

        {/* 快速操作区域 */}
        <Animated.View style={[styles.quickActionsSection, { opacity: fadeAnim }]}>
          {/* 快速加入房间 */}
          <Card style={styles.quickJoinCard} variant="vibrant">
            <View style={styles.quickJoinHeader}>
              <Text style={styles.quickJoinEmoji}>🚀</Text>
              <Text style={styles.quickJoinTitle}>快速加入</Text>
            </View>
            <View style={styles.joinInputContainer}>
              <TextInput
                style={styles.joinInput}
                placeholder="输入房间代码"
                value={roomCodeInput}
                onChangeText={setRoomCodeInput}
                autoCapitalize="characters"
                maxLength={6}
                placeholderTextColor={theme.colors.textTertiary}
              />
              <Button
                title="加入"
                onPress={handleJoinRoom}
                size="medium"
                disabled={!roomCodeInput.trim()}
              />
            </View>
          </Card>

          {/* 签到功能 */}
          <Card style={styles.checkInCard} variant="filled">
            <View style={styles.checkInHeader}>
              <Text style={styles.checkInEmoji}>📅</Text>
              <Text style={styles.checkInTitle}>每日签到</Text>
            </View>
            <CheckInButton
              onCheckInSuccess={() => {
                console.log('签到成功！');
              }}
              style={styles.checkInButtonContainer}
              compact={true}
            />
          </Card>
        </Animated.View>

        {/* 主要功能区域 */}
        <Animated.View style={[styles.mainFeaturesSection, { opacity: fadeAnim }]}>
          <Text style={styles.sectionTitle}>🎯 开始团建</Text>
          <View style={styles.featuresGrid}>
            {navigationCards.map(renderNavigationCard)}
          </View>
        </Animated.View>

        {/* 底部区域 */}
        <View style={styles.bottomSection}>
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={logout}
          >
            <Text style={styles.logoutText}>退出登录</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: theme.spacing['3xl'],
  },

  // 背景装饰
  backgroundDecoration: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 300,
    zIndex: -1,
  },
  circle1: {
    position: 'absolute',
    top: -50,
    right: -30,
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: theme.colors.primary + '15',
  },
  circle2: {
    position: 'absolute',
    top: 80,
    left: -40,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.secondary + '20',
  },
  circle3: {
    position: 'absolute',
    top: 150,
    right: 50,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.tertiary + '25',
  },

  // 英雄区域
  heroSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingTop: theme.spacing['2xl'],
    paddingBottom: theme.spacing.xl,
    alignItems: 'center',
  },
  welcomeContainer: {
    alignItems: 'center',
  },
  welcomeEmoji: {
    fontSize: 48,
    marginBottom: theme.spacing.sm,
  },
  welcomeTitle: {
    fontSize: theme.typography.fontSize['2xl'],
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
  },
  welcomeSubtitle: {
    fontSize: theme.typography.fontSize.lg,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.md,
  },
  subscriptionBadge: {
    marginTop: theme.spacing.sm,
  },

  // 快速操作区域
  quickActionsSection: {
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  quickJoinCard: {
    marginBottom: theme.spacing.md,
    backgroundColor: theme.colors.primary + '10',
    borderColor: theme.colors.primary + '30',
  },
  quickJoinHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  quickJoinEmoji: {
    fontSize: 24,
    marginRight: theme.spacing.sm,
  },
  quickJoinTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  joinInputContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    alignItems: 'center',
  },
  joinInput: {
    flex: 1,
    height: 44,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textPrimary,
    backgroundColor: theme.colors.surface,
  },

  // 签到区域
  checkInCard: {
    backgroundColor: theme.colors.secondary + '15',
    borderColor: theme.colors.secondary + '30',
  },
  checkInHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  checkInEmoji: {
    fontSize: 20,
    marginRight: theme.spacing.sm,
  },
  checkInTitle: {
    fontSize: theme.typography.fontSize.base,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  checkInButtonContainer: {
    alignItems: 'center',
  },

  // 主要功能区域
  mainFeaturesSection: {
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSize.xl,
    fontWeight: theme.typography.fontWeight.bold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.lg,
    textAlign: 'center',
  },
  featuresGrid: {
    gap: theme.spacing.md,
  },

  // 导航卡片
  navigationCard: {
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
  },
  cardContent: {
    gap: theme.spacing.sm,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  cardIcon: {
    fontSize: theme.typography.fontSize['2xl'],
  },
  cardTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardTitle: {
    fontSize: theme.typography.fontSize.lg,
    fontWeight: theme.typography.fontWeight.semibold,
    color: theme.colors.textPrimary,
  },
  cardDescription: {
    fontSize: theme.typography.fontSize.sm,
    color: theme.colors.textSecondary,
    marginLeft: theme.typography.fontSize['2xl'] + theme.spacing.md, // 对齐图标
  },

  // 底部区域
  bottomSection: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.xl,
    alignItems: 'center',
  },
  logoutButton: {
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  logoutText: {
    fontSize: theme.typography.fontSize.base,
    color: theme.colors.textSecondary,
    fontWeight: theme.typography.fontWeight.medium,
  },
});
