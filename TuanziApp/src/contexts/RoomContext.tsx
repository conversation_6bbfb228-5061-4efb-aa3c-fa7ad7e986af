// TuanziApp/src/contexts/RoomContext.tsx

import React, { createContext, useContext, useState, useCallback, ReactNode, useRef } from 'react';
import { Alert } from 'react-native';
import { Room, Message, PathData, PictionaryState, WSMessage } from '../types';
import { useWebSocket } from '../hooks/useWebSocket';

/**
 * 房间状态管理上下文
 * 统一管理房间相关的状态和WebSocket连接
 */

interface RoomContextType {
  // 房间基本信息
  room: Room | null;
  setRoom: (room: Room | null) => void;
  
  // 连接状态
  isConnected: boolean;
  
  // 房间状态
  roomStatus: string;
  setRoomStatus: (status: string) => void;
  
  // 当前步骤
  currentStep: any;
  setCurrentStep: (step: any) => void;
  stepPayload: any;
  setStepPayload: (payload: any) => void;
  
  // 聊天消息
  messages: Message[];
  addMessage: (message: Message) => void;
  sendMessage: (message: string) => void;
  sendGuess: (guess: string) => void;
  
  // 绘图数据
  paths: PathData[];
  addPath: (path: PathData) => void;
  clearPaths: () => void;
  sendDrawing: (pathData: PathData) => void;
  
  // 你画我猜状态
  pictionaryState: PictionaryState | null;
  setPictionaryState: (state: PictionaryState | null) => void;
  
  // 房间操作
  nextStep: () => void;
  restartGame: (gameType: string) => void;
  returnToLobby: () => void;

  // 连接管理
  connect: (roomCode: string, token: string) => void;
  disconnect: () => void;
  reconnect: () => void;
}

const RoomContext = createContext<RoomContextType | undefined>(undefined);

interface RoomProviderProps {
  children: ReactNode;
}

export const RoomProvider: React.FC<RoomProviderProps> = ({ children }) => {
  // 房间基本信息
  const [room, setRoom] = useState<Room | null>(null);
  
  // 房间状态 - 初始状态设为READY，因为房间创建后会自动转换到READY状态
  const [roomStatus, setRoomStatus] = useState('READY');
  const [currentStep, setCurrentStep] = useState<any>(null);
  const [stepPayload, setStepPayload] = useState<any>(null);
  
  // 聊天消息
  const [messages, setMessages] = useState<Message[]>([]);
  
  // 绘图数据
  const [paths, setPaths] = useState<PathData[]>([]);
  const localPathIds = useRef<Set<string>>(new Set());
  
  // 你画我猜状态
  const [pictionaryState, setPictionaryState] = useState<PictionaryState | null>(null);
  
  // 绘图节流
  const drawingThrottle = useRef<{
    lastSentTime: number;
    pendingPath: PathData | null;
    timeoutId: number | null;
  }>({
    lastSentTime: 0,
    pendingPath: null,
    timeoutId: null
  });

  // WebSocket消息处理
  const handleWebSocketMessage = useCallback((message: WSMessage) => {
    console.log('Received WebSocket message:', message);
    
    switch (message.type) {
      case 'step_started':
        setCurrentStep(message.payload.step);
        setStepPayload(message.payload);
        setRoomStatus('IN_PROGRESS');
        
        if (message.payload.step?.step_type === 'DRAWING_GAME') {
          setPictionaryState(message.payload.pictionary_state);
          setPaths([]);
          localPathIds.current.clear();
        }
        break;
        
      case 'round_over':
        const alertTitle = message.payload.step?.step_type === 'DRAWING_GAME' ? "回合结束" : "环节结束";
        const alertMessage = message.payload.message;
        
        if (room?.host) {
          Alert.alert(
            alertTitle,
            alertMessage + "\n\n是否再开始一轮？",
            [
              { text: "结束", style: "cancel" },
              {
                text: "再来一轮",
                onPress: () => restartGame('PICTIONARY')
              }
            ]
          );
        } else {
          Alert.alert(alertTitle, alertMessage);
        }
        break;
        
      case 'event_finished':
        Alert.alert("活动结束", message.payload.message);
        setRoomStatus('FINISHED');
        setCurrentStep(null);
        break;

      case 'game_ended':
        // 房间被房主强制结束
        Alert.alert(
          "房间已结束",
          message.payload.message,
          [
            {
              text: "确定",
              onPress: () => {
                // 强制退出房间
                disconnect();
                // 清理房间状态
                setRoom(null);
                setRoomStatus('WAITING');
                setCurrentStep(null);
                setPictionaryState(null);
                clearPaths();
                // 注意：导航逻辑需要在使用这个Context的组件中处理
              }
            }
          ],
          { cancelable: false }
        );
        break;

      case 'return_to_lobby':
        // 回到大厅
        // 如果是所有环节完成，显示不同的提示
        if (message.payload.reason === 'all_steps_completed') {
          Alert.alert(
            "环节完成",
            message.payload.message,
            [{ text: "确定" }]
          );
        } else {
          Alert.alert(
            "回到大厅",
            message.payload.message,
            [{ text: "确定" }]
          );
        }
        setRoomStatus('READY');
        setCurrentStep(null);
        setStepPayload(null);
        setPictionaryState(null);
        clearPaths();
        break;

      case 'error':
        Alert.alert("操作失败", message.payload.message);
        break;

      case 'step_added':
        // 处理环节添加消息
        console.log('Step added in context:', message.payload);
        // 这里可以更新房间状态或环节列表
        break;

      case 'chat_message':
        addMessage(message.payload);
        break;
        
      case 'drawing_data':
        const incomingPath = message.payload.path_data;
        if (!localPathIds.current.has(incomingPath.id)) {
          addPath(incomingPath);
        }
        break;
        
      case 'step_timeout':
        setCurrentStep(null);
        setStepPayload(null);
        setRoomStatus(message.payload.room_status);
        setPictionaryState(null);
        clearPaths();
        Alert.alert("时间到!", "环节时间已结束");
        break;

      case 'participants_update':
        // 处理成员列表更新
        console.log('Participants updated:', message.payload);
        // 重新获取房间信息以更新participants列表
        if (room?.room_code) {
          // 这里可以调用API重新获取房间信息，或者从消息中更新
          // 暂时只更新房间状态
          if (message.payload.room_status) {
            setRoomStatus(message.payload.room_status);
          }
        }
        break;

      case 'room_state_update':
        // 处理房间状态更新
        console.log('Room state updated:', message.payload);
        if (message.payload.room_status) {
          setRoomStatus(message.payload.room_status);
        }
        break;

      case 'session_timeout':
        // 处理会话超时
        Alert.alert(
          "会话超时",
          message.payload.message || "由于长时间无活动，您的会话已超时",
          [
            {
              text: "确定",
              onPress: () => {
                disconnect();
                // 清理房间状态
                setRoom(null);
                setRoomStatus('WAITING');
                setCurrentStep(null);
                setPictionaryState(null);
                clearPaths();
              }
            }
          ],
          { cancelable: false }
        );
        break;

      case 'heartbeat_response':
        // 心跳响应，不需要特殊处理
        break;

      default:
        console.warn('Unknown message type:', message.type);
    }
  }, [room?.host]);

  // 使用统一的WebSocket Hook
  const {
    isConnected,
    sendMessage: sendWSMessage,
    reconnect,
    disconnect: wsDisconnect,
    connect: wsConnect
  } = useWebSocket({
    onMessage: handleWebSocketMessage,
    maxReconnectAttempts: 5,
    reconnectDelay: 1000,
    autoConnect: false, // 手动控制连接
  });

  // 聊天消息管理
  const addMessage = useCallback((message: Message) => {
    setMessages(prev => [message, ...prev]);
  }, []);

  const sendMessage = useCallback((message: string) => {
    sendWSMessage('send_message', { message });
  }, [sendWSMessage]);

  const sendGuess = useCallback((guess: string) => {
    sendWSMessage('guess_word', { guess });
  }, [sendWSMessage]);

  // 绘图数据管理
  const addPath = useCallback((path: PathData) => {
    setPaths(prev => [...prev, path]);
  }, []);

  const clearPaths = useCallback(() => {
    setPaths([]);
    localPathIds.current.clear();
  }, []);

  const sendDrawing = useCallback((pathData: PathData) => {
    localPathIds.current.add(pathData.id);
    addPath(pathData);

    const now = Date.now();
    const THROTTLE_MS = 50;

    if (now - drawingThrottle.current.lastSentTime >= THROTTLE_MS) {
      drawingThrottle.current.lastSentTime = now;
      sendWSMessage('send_drawing', { path_data: pathData });
    } else {
      drawingThrottle.current.pendingPath = pathData;

      if (drawingThrottle.current.timeoutId) {
        clearTimeout(drawingThrottle.current.timeoutId);
      }

      const remainingTime = THROTTLE_MS - (now - drawingThrottle.current.lastSentTime);
      drawingThrottle.current.timeoutId = setTimeout(() => {
        if (drawingThrottle.current.pendingPath) {
          drawingThrottle.current.lastSentTime = Date.now();
          sendWSMessage('send_drawing', {
            path_data: drawingThrottle.current.pendingPath
          });
          drawingThrottle.current.pendingPath = null;
        }
        drawingThrottle.current.timeoutId = null;
      }, remainingTime);
    }
  }, [sendWSMessage, addPath]);

  // 房间操作
  const nextStep = useCallback(() => {
    sendWSMessage('next_step');
  }, [sendWSMessage]);

  const restartGame = useCallback((gameType: string) => {
    sendWSMessage('restart_game', { game_type: gameType });
  }, [sendWSMessage]);

  const returnToLobby = useCallback(() => {
    sendWSMessage('return_to_lobby', {});
  }, [sendWSMessage]);

  // 连接管理
  const connect = useCallback((roomCode: string, token: string) => {
    wsConnect(roomCode, token);
    // 更新房间信息
    if (room?.room_code !== roomCode) {
      setRoom(prev => prev ? { ...prev, room_code: roomCode } : null);
    }
  }, [wsConnect, room]);

  const disconnect = useCallback(() => {
    wsDisconnect();
    // 清理状态
    setMessages([]);
    clearPaths();
    setPictionaryState(null);
    setCurrentStep(null);
    setStepPayload(null);
    setRoomStatus('WAITING');
  }, [wsDisconnect, clearPaths]);

  const contextValue: RoomContextType = {
    // 房间基本信息
    room,
    setRoom,
    
    // 连接状态
    isConnected,
    
    // 房间状态
    roomStatus,
    setRoomStatus,
    currentStep,
    setCurrentStep,
    stepPayload,
    setStepPayload,
    
    // 聊天消息
    messages,
    addMessage,
    sendMessage,
    sendGuess,
    
    // 绘图数据
    paths,
    addPath,
    clearPaths,
    sendDrawing,
    
    // 你画我猜状态
    pictionaryState,
    setPictionaryState,
    
    // 房间操作
    nextStep,
    restartGame,
    returnToLobby,

    // 连接管理
    connect,
    disconnect,
    reconnect,
  };

  return (
    <RoomContext.Provider value={contextValue}>
      {children}
    </RoomContext.Provider>
  );
};

/**
 * 使用房间上下文的Hook
 */
export const useRoom = (): RoomContextType => {
  const context = useContext(RoomContext);
  if (context === undefined) {
    throw new Error('useRoom must be used within a RoomProvider');
  }
  return context;
};
